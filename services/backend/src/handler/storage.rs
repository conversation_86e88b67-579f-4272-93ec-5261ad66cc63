use std::sync::Arc;
use std::path::Path;

use axum::{
    extract::{Multipart, Path as AxumPath, Query},
    http::{header, HeaderMap, StatusCode},
    middleware,
    response::{IntoResponse, Response},
    routing::{get, post},
    Extension, Json, Router,
};
use bytes::Bytes;
use futures_util::TryStreamExt;
use uuid::Uuid;

use crate::{
    db::{OrganizationExt, StorageExt},
    dtos::{FileInfoDto, FileListResponseDto, FilePermissionDto, FilePermissionResponseDto, FileUploadResponseDto, RequestQueryDto},
    error::HttpError,
    middleware::{auth, JWTAuthMiddeware},
    models::{FileAccessType, FileType, PermissionType},
    AppState,
};

const MAX_FILE_SIZE: usize = 100 * 1024 * 1024; // 100MB
const UPLOAD_DIR: &str = "uploads";

pub fn storage_handler(_app_state: Arc<AppState>) -> Router {
    Router::new()
        // Public endpoints - no authentication required
        .route("/public/upload", post(upload_public_files))
        .route("/public/:file_id", get(get_public_file))

        // Private endpoints - authentication required
        .nest("/private", Router::new()
            .route("/upload", post(upload_private_files))
            .route("/:file_id", get(get_private_file))
            .route("/:file_id/permissions", post(manage_file_permissions))
            .route("/my-files", get(get_user_files))
            .layer(middleware::from_fn(auth))
        )
}

async fn upload_public_files(
    Extension(app_state): Extension<Arc<AppState>>,
    mut multipart: Multipart,
) -> Result<impl IntoResponse, HttpError> {
    let mut uploaded_files = Vec::new();

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        HttpError::bad_request(format!("Failed to read multipart field: {}", e))
    })? {
        if let Some(filename) = field.file_name() {
            let filename = filename.to_string();
            let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
            let data = field.bytes().await.map_err(|e| {
                HttpError::bad_request(format!("Failed to read file data: {}", e))
            })?;

            if data.len() > MAX_FILE_SIZE {
                return Err(HttpError::bad_request(format!(
                    "File {} exceeds maximum size of {}MB",
                    filename,
                    MAX_FILE_SIZE / (1024 * 1024)
                )));
            }

            let file_info = process_file_upload(
                &app_state,
                &filename,
                &content_type,
                data,
                FileAccessType::Public,
                None,
                None,
            ).await?;

            uploaded_files.push(file_info);
        }
    }

    if uploaded_files.is_empty() {
        return Err(HttpError::bad_request("No files were uploaded".to_string()));
    }

    let response = FileUploadResponseDto {
        status: "success".to_string(),
        message: format!("Successfully uploaded {} file(s)", uploaded_files.len()),
        files: uploaded_files,
    };

    Ok(Json(response))
}

async fn upload_private_files(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    mut multipart: Multipart,
) -> Result<impl IntoResponse, HttpError> {
    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Get user's default organization if any
    let organization_id = auth.user.default_organization_id;

    let mut uploaded_files = Vec::new();

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        HttpError::bad_request(format!("Failed to read multipart field: {}", e))
    })? {
        if let Some(filename) = field.file_name() {
            let filename = filename.to_string();
            let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
            let data = field.bytes().await.map_err(|e| {
                HttpError::bad_request(format!("Failed to read file data: {}", e))
            })?;

            if data.len() > MAX_FILE_SIZE {
                return Err(HttpError::bad_request(format!(
                    "File {} exceeds maximum size of {}MB",
                    filename,
                    MAX_FILE_SIZE / (1024 * 1024)
                )));
            }

            let access_type = if organization_id.is_some() {
                FileAccessType::Organization
            } else {
                FileAccessType::Private
            };

            let file_info = process_file_upload(
                &app_state,
                &filename,
                &content_type,
                data,
                access_type,
                Some(user_id),
                organization_id,
            ).await?;

            uploaded_files.push(file_info);
        }
    }

    if uploaded_files.is_empty() {
        return Err(HttpError::bad_request("No files were uploaded".to_string()));
    }

    let response = FileUploadResponseDto {
        status: "success".to_string(),
        message: format!("Successfully uploaded {} file(s)", uploaded_files.len()),
        files: uploaded_files,
    };

    Ok(Json(response))
}

async fn process_file_upload(
    app_state: &Arc<AppState>,
    original_filename: &str,
    content_type: &str,
    data: Bytes,
    access_type: FileAccessType,
    uploaded_by: Option<Uuid>,
    organization_id: Option<Uuid>,
) -> Result<FileInfoDto, HttpError> {
    // Generate unique filename
    let file_id = Uuid::new_v4();
    let extension = Path::new(original_filename)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");

    let filename = if extension.is_empty() {
        file_id.to_string()
    } else {
        format!("{}.{}", file_id, extension)
    };

    // Determine file type
    let file_type = FileType::from_filename(original_filename);

    // Create upload directory if it doesn't exist
    let upload_path = std::path::Path::new(UPLOAD_DIR);
    if !upload_path.exists() {
        std::fs::create_dir_all(upload_path).map_err(|e| {
            HttpError::server_error(format!("Failed to create upload directory: {}", e))
        })?;
    }

    // Save file to disk
    let file_path = upload_path.join(&filename);
    std::fs::write(&file_path, &data).map_err(|e| {
        HttpError::server_error(format!("Failed to save file: {}", e))
    })?;

    // Create metadata
    let mut metadata = serde_json::Map::new();
    metadata.insert("size_bytes".to_string(), serde_json::Value::Number(serde_json::Number::from(data.len())));

    // Add file-type specific metadata
    match file_type {
        FileType::Image => {
            // Could add image dimensions here if needed
            metadata.insert("type".to_string(), serde_json::Value::String("image".to_string()));
        },
        FileType::Code | FileType::Markdown | FileType::Html => {
            // Could add line count, language detection, etc.
            metadata.insert("type".to_string(), serde_json::Value::String("text".to_string()));
        },
        _ => {}
    }

    // Save file record to database
    let file = app_state.db_client.create_file(
        filename.clone(),
        original_filename.to_string(),
        file_path.to_string_lossy().to_string(),
        data.len() as i64,
        content_type.to_string(),
        file_type,
        access_type,
        uploaded_by,
        organization_id,
        Some(serde_json::Value::Object(metadata)),
    ).await.map_err(|e| {
        HttpError::server_error(format!("Failed to save file record: {}", e))
    })?;

    // Get base URL from config
    let base_url = &app_state.env.frontend_url;

    Ok(FileInfoDto::from_file(&file, base_url))
}

async fn get_public_file(
    Extension(app_state): Extension<Arc<AppState>>,
    AxumPath(file_id): AxumPath<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Response, HttpError> {
    let file_id = Uuid::parse_str(&file_id)
        .map_err(|_| HttpError::bad_request("Invalid file ID".to_string()))?;

    let file = app_state.db_client.get_file(file_id).await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("File not found".to_string()))?;

    // Check if file is public
    if file.access_type != FileAccessType::Public {
        return Err(HttpError::forbidden("File is not publicly accessible".to_string()));
    }

    serve_file(&app_state, &file, None, params.get("download").is_some()).await
}

async fn get_private_file(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    AxumPath(file_id): AxumPath<String>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Response, HttpError> {
    let file_id = Uuid::parse_str(&file_id)
        .map_err(|_| HttpError::bad_request("Invalid file ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    let file = app_state.db_client.get_file(file_id).await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("File not found".to_string()))?;

    // Check access permissions
    let has_access = match file.access_type {
        FileAccessType::Public => true,
        FileAccessType::Private => {
            // User must be the owner or have explicit permission
            file.uploaded_by == Some(user_id) ||
            app_state.db_client.check_file_permission(
                file_id,
                Some(user_id),
                None,
                PermissionType::Read
            ).await.unwrap_or(false)
        },
        FileAccessType::Organization => {
            // User must be member of the organization or have explicit permission
            if let Some(org_id) = file.organization_id {
                let is_member = app_state.db_client.get_organization_member(org_id, user_id).await
                    .unwrap_or(None).is_some();

                is_member || app_state.db_client.check_file_permission(
                    file_id,
                    Some(user_id),
                    Some(org_id),
                    PermissionType::Read
                ).await.unwrap_or(false)
            } else {
                false
            }
        }
    };

    if !has_access {
        return Err(HttpError::forbidden("Access denied".to_string()));
    }

    serve_file(&app_state, &file, Some(user_id), params.get("download").is_some()).await
}

async fn serve_file(
    app_state: &Arc<AppState>,
    file: &crate::models::File,
    user_id: Option<Uuid>,
    force_download: bool,
) -> Result<Response, HttpError> {
    // Read file from disk
    let file_data = std::fs::read(&file.file_path).map_err(|e| {
        HttpError::server_error(format!("Failed to read file: {}", e))
    })?;

    // Update download count
    if let Err(e) = app_state.db_client.update_file_download_count(file.id).await {
        eprintln!("Failed to update download count: {}", e);
    }

    // Log file access
    let access_type = if force_download { "download" } else { "view" };
    if let Err(e) = app_state.db_client.log_file_access(
        file.id,
        user_id,
        None, // Could extract IP from request headers
        None, // Could extract User-Agent from request headers
        access_type.to_string(),
    ).await {
        eprintln!("Failed to log file access: {}", e);
    }

    // Set appropriate headers
    let mut headers = HeaderMap::new();
    headers.insert(header::CONTENT_TYPE, file.mime_type.parse().unwrap());
    headers.insert(header::CONTENT_LENGTH, file_data.len().to_string().parse().unwrap());

    if force_download {
        let disposition = format!("attachment; filename=\"{}\"", file.original_filename);
        headers.insert(header::CONTENT_DISPOSITION, disposition.parse().unwrap());
    } else {
        let disposition = format!("inline; filename=\"{}\"", file.original_filename);
        headers.insert(header::CONTENT_DISPOSITION, disposition.parse().unwrap());
    }

    Ok((StatusCode::OK, headers, file_data).into_response())
}

async fn get_user_files(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Query(query): Query<RequestQueryDto>,
) -> Result<impl IntoResponse, HttpError> {
    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    let page = query.page.unwrap_or(1) as u32;
    let limit = query.limit.unwrap_or(10).min(50);

    let files = app_state.db_client.get_files(
        Some(user_id),
        None,
        None,
        None,
        page,
        limit,
    ).await.map_err(|e| HttpError::server_error(e.to_string()))?;

    let total_count = app_state.db_client.get_file_count(
        Some(user_id),
        None,
        None,
        None,
    ).await.map_err(|e| HttpError::server_error(e.to_string()))?;

    let response = FileListResponseDto {
        status: "success".to_string(),
        files: crate::dtos::FilterFileDto::filter_files(&files),
        results: total_count,
    };

    Ok(Json(response))
}

async fn manage_file_permissions(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    AxumPath(file_id): AxumPath<String>,
    Json(body): Json<FilePermissionDto>,
) -> Result<impl IntoResponse, HttpError> {
    let file_id = Uuid::parse_str(&file_id)
        .map_err(|_| HttpError::bad_request("Invalid file ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if user owns the file or has admin permission
    let file = app_state.db_client.get_file(file_id).await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("File not found".to_string()))?;

    let can_manage = file.uploaded_by == Some(user_id) ||
        app_state.db_client.check_file_permission(
            file_id,
            Some(user_id),
            None,
            PermissionType::Admin
        ).await.unwrap_or(false);

    if !can_manage {
        return Err(HttpError::forbidden("You don't have permission to manage this file".to_string()));
    }

    // Parse permission type
    let permission_type = match body.permission_type.as_str() {
        "read" => PermissionType::Read,
        "write" => PermissionType::Write,
        "delete" => PermissionType::Delete,
        "admin" => PermissionType::Admin,
        _ => return Err(HttpError::bad_request("Invalid permission type".to_string())),
    };

    // Parse user_id or organization_id
    let target_user_id = body.user_id.as_ref()
        .map(|id| Uuid::parse_str(id))
        .transpose()
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    let target_org_id = body.organization_id.as_ref()
        .map(|id| Uuid::parse_str(id))
        .transpose()
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    if target_user_id.is_none() && target_org_id.is_none() {
        return Err(HttpError::bad_request("Either user_id or organization_id must be provided".to_string()));
    }

    // Create permission
    let _permission = app_state.db_client.create_file_permission(
        file_id,
        target_user_id,
        target_org_id,
        permission_type,
        Some(user_id),
        body.expires_at,
    ).await.map_err(|e| HttpError::server_error(e.to_string()))?;

    let response = FilePermissionResponseDto {
        status: "success".to_string(),
        message: "Permission granted successfully".to_string(),
        permissions: vec![], // Could return updated permissions list
    };

    Ok(Json(response))
}
