use async_trait::async_trait;
use chrono::{DateTime, Duration, Utc};
use sqlx::{Pool, Postgres};
use uuid::Uuid;

use crate::models::{File, FileAccessLog, FileAccessType, FilePermission, FileType, Organization, OrganizationInvitation, OrganizationMember, OrganizationRole, PermissionType, User, UserRole};

#[derive(Debug, Clone)]
pub struct DBClient {
    pool: Pool<Postgres>,
}

impl DBClient {

    pub fn new(pool: Pool<Postgres>) -> Self {
        DBClient { pool }
    }

}

#[async_trait]
pub trait UserExt {
    async fn get_user(
        &self,
        user_id: Option<Uuid>,
        name: Option<&str>,
        email: Option<&str>,
    ) -> Result<Option<User>, sqlx::Error>;




    async fn get_users(
        &self,
        page: u32,
        limit: usize,
    ) -> Result<Vec<User>, sqlx::Error>;

    async fn save_user<T: Into<String> + Send>(
        &self,
        name: T,
        email: T,
    ) -> Result<User, sqlx::Error>;

    async fn check_email_exists(&self, email: &str) -> Result<bool, sqlx::Error>;

    async fn set_email_otp<T: Into<String> + Send>(
        &self,
        email: &str,
        otp: T,
        expires_at: DateTime<Utc>,
    ) -> Result<(), sqlx::Error>;

    async fn verify_email_otp(
        &self,
        email: &str,
        otp: &str,
    ) -> Result<bool, sqlx::Error>;

    async fn get_user_count(&self) -> Result<i64, sqlx::Error>;



    async fn update_user_name<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        name: T,
    ) -> Result<User, sqlx::Error>;

    async fn update_new_user<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        name: T,
        profile_data: Option<serde_json::Value>,
    ) -> Result<User, sqlx::Error>;

    async fn update_user_role(
        &self,
        user_id: Uuid,
        role: UserRole,
    ) -> Result<User, sqlx::Error>;

    // Reset user account to new state
    async fn reset_user_account(
        &self,
        email: &str,
    ) -> Result<User, sqlx::Error>;

    // Rate limiting functions
    async fn record_otp_request(&self, email: &str, ip_address: Option<&str>) -> Result<(), sqlx::Error>;

    async fn check_otp_rate_limit(&self, email: &str, max_requests: i64, window_hours: i64) -> Result<bool, sqlx::Error>;

    async fn clear_old_otp_requests(&self, older_than_hours: i64) -> Result<i64, sqlx::Error>;

    async fn update_user_profile<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        new_name: T,
        profile_data: Option<serde_json::Value>,
    ) -> Result<User, sqlx::Error>;

    async fn set_default_organization(
        &self,
        user_id: Uuid,
        organization_id: Uuid,
    ) -> Result<User, sqlx::Error>;
}

#[async_trait]
pub trait OrganizationExt {
    // Organization operations
    async fn create_organization<T: Into<String> + Send>(
        &self,
        name: T,
        description: Option<String>,
        domain: Option<String>,
        settings: Option<serde_json::Value>,
        created_by: Uuid,
    ) -> Result<Organization, sqlx::Error>;

    async fn get_organization(
        &self,
        organization_id: Uuid,
    ) -> Result<Option<Organization>, sqlx::Error>;

    async fn get_user_organizations(
        &self,
        user_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<Organization>, sqlx::Error>;

    async fn get_organization_count_for_user(
        &self,
        user_id: Uuid,
    ) -> Result<i64, sqlx::Error>;

    async fn update_organization<T: Into<String> + Send>(
        &self,
        organization_id: Uuid,
        name: T,
        description: Option<String>,
        domain: Option<String>,
        settings: Option<serde_json::Value>,
    ) -> Result<Organization, sqlx::Error>;

    async fn delete_organization(
        &self,
        organization_id: Uuid,
    ) -> Result<(), sqlx::Error>;

    // Organization member operations
    async fn add_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
        role: OrganizationRole,
        invited_by: Option<Uuid>,
    ) -> Result<OrganizationMember, sqlx::Error>;

    async fn get_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
    ) -> Result<Option<OrganizationMember>, sqlx::Error>;

    async fn get_organization_member_by_id(
        &self,
        member_id: Uuid,
    ) -> Result<Option<OrganizationMember>, sqlx::Error>;

    async fn get_organization_members(
        &self,
        organization_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<(OrganizationMember, User)>, sqlx::Error>;

    async fn get_organization_member_count(
        &self,
        organization_id: Uuid,
    ) -> Result<i64, sqlx::Error>;

    async fn update_organization_member_role(
        &self,
        member_id: Uuid,
        role: OrganizationRole,
    ) -> Result<OrganizationMember, sqlx::Error>;

    async fn remove_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
    ) -> Result<(), sqlx::Error>;

    // Organization invitation operations
    async fn create_organization_invitation<T: Into<String> + Send>(
        &self,
        organization_id: Uuid,
        email: T,
        role: OrganizationRole,
        invited_by: Uuid,
        token: String,
        expires_at: DateTime<Utc>,
    ) -> Result<OrganizationInvitation, sqlx::Error>;

    async fn get_invitation_by_token(
        &self,
        token: &str,
    ) -> Result<Option<(OrganizationInvitation, Organization, User)>, sqlx::Error>;

    async fn get_invitation_by_email(
        &self,
        organization_id: Uuid,
        email: &str,
    ) -> Result<Option<OrganizationInvitation>, sqlx::Error>;

    async fn update_invitation_status(
        &self,
        invitation_id: Uuid,
        status: &str,
    ) -> Result<OrganizationInvitation, sqlx::Error>;

    async fn get_user_invitations(
        &self,
        email: &str,
    ) -> Result<Vec<(OrganizationInvitation, Organization, User)>, sqlx::Error>;

    async fn get_organization_invitations(
        &self,
        organization_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<(OrganizationInvitation, User)>, sqlx::Error>;

    async fn get_organization_invitation_count(
        &self,
        organization_id: Uuid,
    ) -> Result<i64, sqlx::Error>;
}

#[async_trait]
pub trait StorageExt {
    // File operations
    async fn create_file<T: Into<String> + Send>(
        &self,
        filename: T,
        original_filename: T,
        file_path: T,
        file_size: i64,
        mime_type: T,
        file_type: FileType,
        access_type: FileAccessType,
        uploaded_by: Option<Uuid>,
        organization_id: Option<Uuid>,
        metadata: Option<serde_json::Value>,
    ) -> Result<File, sqlx::Error>;

    async fn get_file(
        &self,
        file_id: Uuid,
    ) -> Result<Option<File>, sqlx::Error>;

    async fn get_files(
        &self,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        access_type: Option<FileAccessType>,
        file_type: Option<FileType>,
        page: u32,
        limit: usize,
    ) -> Result<Vec<File>, sqlx::Error>;

    async fn get_file_count(
        &self,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        access_type: Option<FileAccessType>,
        file_type: Option<FileType>,
    ) -> Result<i64, sqlx::Error>;

    async fn update_file_download_count(
        &self,
        file_id: Uuid,
    ) -> Result<(), sqlx::Error>;

    async fn delete_file(
        &self,
        file_id: Uuid,
    ) -> Result<(), sqlx::Error>;

    // File permission operations
    async fn create_file_permission(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        permission_type: PermissionType,
        granted_by: Option<Uuid>,
        expires_at: Option<DateTime<Utc>>,
    ) -> Result<FilePermission, sqlx::Error>;

    async fn get_file_permissions(
        &self,
        file_id: Uuid,
    ) -> Result<Vec<FilePermission>, sqlx::Error>;

    async fn check_file_permission(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        permission_type: PermissionType,
    ) -> Result<bool, sqlx::Error>;

    async fn delete_file_permission(
        &self,
        permission_id: Uuid,
    ) -> Result<(), sqlx::Error>;

    // File access logging
    async fn log_file_access(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        ip_address: Option<String>,
        user_agent: Option<String>,
        access_type: String,
    ) -> Result<(), sqlx::Error>;

    async fn get_file_access_logs(
        &self,
        file_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<FileAccessLog>, sqlx::Error>;
}


#[async_trait]
impl UserExt for DBClient {
    async fn get_user(
        &self,
        user_id: Option<Uuid>,
        name: Option<&str>,
        email: Option<&str>,
    ) -> Result<Option<User>, sqlx::Error> {
        let mut user: Option<User> = None;

        if let Some(user_id) = user_id {
            user = sqlx::query_as!(
                User,
                r#"SELECT id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at FROM users WHERE id = $1"#,
                user_id
            ).fetch_optional(&self.pool).await?;
        } else if let Some(name) = name {
            user = sqlx::query_as!(
                User,
                r#"SELECT id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at FROM users WHERE name = $1"#,
                name
            ).fetch_optional(&self.pool).await?;
        } else if let Some(email) = email {
            user = sqlx::query_as!(
                User,
                r#"SELECT id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at FROM users WHERE email = $1"#,
                email
            ).fetch_optional(&self.pool).await?;
        }

        Ok(user)
    }

    async fn get_users(
        &self,
        page: u32,
        limit: usize,
    ) -> Result<Vec<User>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let users = sqlx::query_as!(
            User,
            r#"SELECT id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at FROM users
            ORDER BY created_at DESC LIMIT $1 OFFSET $2"#,
            limit as i64,
            offset as i64,
        ).fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    async fn save_user<T: Into<String> + Send>(
        &self,
        name: T,
        email: T,
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (name, email, profile_data)
            VALUES ($1, $2, '{}'::jsonb)
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            name.into(),
            email.into()
        ).fetch_one(&self.pool)
        .await?;
        Ok(user)
    }

    async fn check_email_exists(&self, email: &str) -> Result<bool, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"SELECT COUNT(*) FROM users WHERE email = $1"#,
            email
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0) > 0)
    }

    async fn set_email_otp<T: Into<String> + Send>(
        &self,
        email: &str,
        otp: T,
        expires_at: DateTime<Utc>,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE users
            SET email_otp = $1, email_otp_expires_at = $2, updated_at = NOW()
            WHERE email = $3
            "#,
            otp.into(),
            expires_at,
            email
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn verify_email_otp(
        &self,
        email: &str,
        otp: &str,
    ) -> Result<bool, sqlx::Error> {
        let result = sqlx::query!(
            r#"
            SELECT email_otp, email_otp_expires_at
            FROM users
            WHERE email = $1
            "#,
            email
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = result {
            if let (Some(stored_otp), Some(expires_at)) = (row.email_otp, row.email_otp_expires_at) {
                if stored_otp == otp && Utc::now() < expires_at {
                    // Clear the OTP after successful verification
                    sqlx::query!(
                        r#"
                        UPDATE users
                        SET email_otp = NULL, email_otp_expires_at = NULL, updated_at = NOW()
                        WHERE email = $1
                        "#,
                        email
                    )
                    .execute(&self.pool)
                    .await?;

                    return Ok(true);
                }
            }
        }

        Ok(false)
    }

    async fn get_user_count(&self) -> Result<i64, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"SELECT COUNT(*) FROM users"#
        )
       .fetch_one(&self.pool)
       .await?;

        Ok(count.unwrap_or(0))
    }




    async fn update_user_name<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        new_name: T
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET name = $1, updated_at = Now()
            WHERE id = $2
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            new_name.into(),
            user_id
        ).fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn update_new_user<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        new_name: T,
        profile_data: Option<serde_json::Value>,
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET name = $1, profile_data = $3, updated_at = Now()
            WHERE id = $2
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            new_name.into(),
            user_id,
            profile_data
        ).fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn update_user_role(
        &self,
        user_id: Uuid,
        new_role: UserRole
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET role = $1, updated_at = Now()
            WHERE id = $2
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            new_role as UserRole,
            user_id
        ).fetch_one(&self.pool)
       .await?;

        Ok(user)
    }

    async fn reset_user_account(
        &self,
        email: &str,
    ) -> Result<User, sqlx::Error> {
        // Reset the user account by setting name to "New User" and clearing profile fields
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET name = 'New User', profile_data = '{}'::jsonb, updated_at = NOW()
            WHERE email = $1
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            email
        ).fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    // Rate limiting implementation
    async fn record_otp_request(&self, email: &str, ip_address: Option<&str>) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            INSERT INTO otp_requests (email, ip_address)
            VALUES ($1, $2)
            "#,
            email,
            ip_address
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn check_otp_rate_limit(&self, email: &str, max_requests: i64, window_hours: i64) -> Result<bool, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM otp_requests
            WHERE email = $1
            AND request_time > NOW() - INTERVAL '1 hour' * $2
            "#,
            email,
            window_hours as f64
        )
        .fetch_one(&self.pool)
        .await?;

        // Return true if the user has exceeded the rate limit
        Ok(count.unwrap_or(0) >= max_requests)
    }

    async fn clear_old_otp_requests(&self, older_than_hours: i64) -> Result<i64, sqlx::Error> {
        let result = sqlx::query!(
            r#"
            DELETE FROM otp_requests
            WHERE request_time < NOW() - INTERVAL '1 hour' * $1
            "#,
            older_than_hours as f64
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() as i64)
    }

    async fn update_user_profile<T: Into<String> + Send>(
        &self,
        user_id: Uuid,
        new_name: T,
        profile_data: Option<serde_json::Value>,
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET name = $1, profile_data = $2, updated_at = Now()
            WHERE id = $3
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            new_name.into(),
            profile_data,
            user_id
        ).fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn set_default_organization(
        &self,
        user_id: Uuid,
        organization_id: Uuid,
    ) -> Result<User, sqlx::Error> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users
            SET default_organization_id = $1, updated_at = Now()
            WHERE id = $2
            RETURNING id, name, email, role as "role: UserRole", email_otp, email_otp_expires_at, profile_data, default_organization_id, created_at, updated_at
            "#,
            organization_id,
            user_id
        ).fetch_one(&self.pool)
        .await?;

        Ok(user)
    }
}

#[async_trait]
impl OrganizationExt for DBClient {
    async fn create_organization<T: Into<String> + Send>(
        &self,
        name: T,
        description: Option<String>,
        domain: Option<String>,
        settings: Option<serde_json::Value>,
        created_by: Uuid,
    ) -> Result<Organization, sqlx::Error> {
        // Start a transaction
        let mut tx = self.pool.begin().await?;

        // Create the organization
        let organization = sqlx::query_as!(
            Organization,
            r#"
            INSERT INTO organizations (name, description, domain, settings)
            VALUES ($1, $2, $3, $4)
            RETURNING id, name, description, domain, settings, created_at, updated_at
            "#,
            name.into(),
            description,
            domain,
            settings
        )
        .fetch_one(&mut *tx)
        .await?;

        // Add the creator as an admin member
        sqlx::query!(
            r#"
            INSERT INTO organization_members (organization_id, user_id, role, joined_at)
            VALUES ($1, $2, 'admin', NOW())
            "#,
            organization.id,
            created_by
        )
        .execute(&mut *tx)
        .await?;

        // Set this as the user's default organization if they don't have one yet
        sqlx::query!(
            r#"
            UPDATE users
            SET default_organization_id = COALESCE(default_organization_id, $1)
            WHERE id = $2
            "#,
            organization.id,
            created_by
        )
        .execute(&mut *tx)
        .await?;

        // Commit the transaction
        tx.commit().await?;

        Ok(organization)
    }

    async fn get_organization(
        &self,
        organization_id: Uuid,
    ) -> Result<Option<Organization>, sqlx::Error> {
        let organization = sqlx::query_as!(
            Organization,
            r#"
            SELECT id, name, description, domain, settings, created_at, updated_at
            FROM organizations
            WHERE id = $1
            "#,
            organization_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(organization)
    }

    async fn get_user_organizations(
        &self,
        user_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<Organization>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let organizations = sqlx::query_as!(
            Organization,
            r#"
            SELECT o.id, o.name, o.description, o.domain, o.settings, o.created_at, o.updated_at
            FROM organizations o
            JOIN organization_members om ON o.id = om.organization_id
            WHERE om.user_id = $1
            ORDER BY o.created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            user_id,
            limit as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(organizations)
    }

    async fn get_organization_count_for_user(
        &self,
        user_id: Uuid,
    ) -> Result<i64, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM organizations o
            JOIN organization_members om ON o.id = om.organization_id
            WHERE om.user_id = $1
            "#,
            user_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0))
    }

    async fn update_organization<T: Into<String> + Send>(
        &self,
        organization_id: Uuid,
        name: T,
        description: Option<String>,
        domain: Option<String>,
        settings: Option<serde_json::Value>,
    ) -> Result<Organization, sqlx::Error> {
        let organization = sqlx::query_as!(
            Organization,
            r#"
            UPDATE organizations
            SET name = $1, description = $2, domain = $3, settings = $4, updated_at = NOW()
            WHERE id = $5
            RETURNING id, name, description, domain, settings, created_at, updated_at
            "#,
            name.into(),
            description,
            domain,
            settings,
            organization_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(organization)
    }

    async fn delete_organization(
        &self,
        organization_id: Uuid,
    ) -> Result<(), sqlx::Error> {
        // Start a transaction
        let mut tx = self.pool.begin().await?;

        // Update users who have this as their default organization
        sqlx::query!(
            r#"
            UPDATE users
            SET default_organization_id = NULL
            WHERE default_organization_id = $1
            "#,
            organization_id
        )
        .execute(&mut *tx)
        .await?;

        // Delete the organization (cascade will delete members and invitations)
        sqlx::query!(
            r#"
            DELETE FROM organizations
            WHERE id = $1
            "#,
            organization_id
        )
        .execute(&mut *tx)
        .await?;

        // Commit the transaction
        tx.commit().await?;

        Ok(())
    }

    async fn add_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
        role: OrganizationRole,
        invited_by: Option<Uuid>,
    ) -> Result<OrganizationMember, sqlx::Error> {
        let member = sqlx::query_as!(
            OrganizationMember,
            r#"
            INSERT INTO organization_members (organization_id, user_id, role, invited_by, joined_at)
            VALUES ($1, $2, $3, $4, NOW())
            RETURNING id, organization_id, user_id, role as "role: OrganizationRole", invited_by, joined_at, settings, created_at, updated_at
            "#,
            organization_id,
            user_id,
            role as OrganizationRole,
            invited_by
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(member)
    }

    async fn get_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
    ) -> Result<Option<OrganizationMember>, sqlx::Error> {
        let member = sqlx::query_as!(
            OrganizationMember,
            r#"
            SELECT id, organization_id, user_id, role as "role: OrganizationRole", invited_by, joined_at, settings, created_at, updated_at
            FROM organization_members
            WHERE organization_id = $1 AND user_id = $2
            "#,
            organization_id,
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(member)
    }

    async fn get_organization_member_by_id(
        &self,
        member_id: Uuid,
    ) -> Result<Option<OrganizationMember>, sqlx::Error> {
        let member = sqlx::query_as!(
            OrganizationMember,
            r#"
            SELECT id, organization_id, user_id, role as "role: OrganizationRole", invited_by, joined_at, settings, created_at, updated_at
            FROM organization_members
            WHERE id = $1
            "#,
            member_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(member)
    }

    async fn get_organization_members(
        &self,
        organization_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<(OrganizationMember, User)>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let rows = sqlx::query!(
            r#"
            SELECT
                om.id as "member_id!", om.organization_id, om.user_id as "member_user_id!", om.role as "role!: OrganizationRole",
                om.invited_by, om.joined_at, om.settings, om.created_at as "member_created_at!", om.updated_at as "member_updated_at!",
                u.id as "user_id!", u.name, u.email, u.role as "user_role!: UserRole", u.email_otp,
                u.email_otp_expires_at, u.profile_data, u.default_organization_id, u.created_at as "user_created_at!", u.updated_at as "user_updated_at!"
            FROM organization_members om
            JOIN users u ON om.user_id = u.id
            WHERE om.organization_id = $1
            ORDER BY om.created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            organization_id,
            limit as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await?;

        let result = rows
            .into_iter()
            .map(|row| {
                let member = OrganizationMember {
                    id: row.member_id,
                    organization_id: row.organization_id,
                    user_id: row.member_user_id,
                    role: row.role,
                    invited_by: row.invited_by,
                    joined_at: Some(row.joined_at),
                    settings: row.settings,
                    created_at: Some(row.member_created_at),
                    updated_at: Some(row.member_updated_at),
                };

                let user = User {
                    id: row.user_id,
                    name: row.name,
                    email: row.email,
                    role: row.user_role,
                    email_otp: row.email_otp,
                    email_otp_expires_at: row.email_otp_expires_at,
                    profile_data: row.profile_data,
                    default_organization_id: row.default_organization_id,
                    created_at: Some(row.user_created_at),
                    updated_at: Some(row.user_updated_at),
                };

                (member, user)
            })
            .collect();

        Ok(result)
    }

    async fn get_organization_member_count(
        &self,
        organization_id: Uuid,
    ) -> Result<i64, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM organization_members
            WHERE organization_id = $1
            "#,
            organization_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0))
    }

    async fn update_organization_member_role(
        &self,
        member_id: Uuid,
        role: OrganizationRole,
    ) -> Result<OrganizationMember, sqlx::Error> {
        let member = sqlx::query_as!(
            OrganizationMember,
            r#"
            UPDATE organization_members
            SET role = $1, updated_at = NOW()
            WHERE id = $2
            RETURNING id, organization_id, user_id, role as "role: OrganizationRole", invited_by, joined_at, settings, created_at, updated_at
            "#,
            role as OrganizationRole,
            member_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(member)
    }

    async fn remove_organization_member(
        &self,
        organization_id: Uuid,
        user_id: Uuid,
    ) -> Result<(), sqlx::Error> {
        // Start a transaction
        let mut tx = self.pool.begin().await?;

        // Check if this is the user's default organization
        let user = sqlx::query!(
            r#"
            SELECT default_organization_id
            FROM users
            WHERE id = $1
            "#,
            user_id
        )
        .fetch_one(&mut *tx)
        .await?;

        // If this is the user's default organization, set it to null
        if let Some(default_org_id) = user.default_organization_id {
            if default_org_id == organization_id {
                sqlx::query!(
                    r#"
                    UPDATE users
                    SET default_organization_id = NULL
                    WHERE id = $1
                    "#,
                    user_id
                )
                .execute(&mut *tx)
                .await?;
            }
        }

        // Remove the member
        sqlx::query!(
            r#"
            DELETE FROM organization_members
            WHERE organization_id = $1 AND user_id = $2
            "#,
            organization_id,
            user_id
        )
        .execute(&mut *tx)
        .await?;

        // Commit the transaction
        tx.commit().await?;

        Ok(())
    }

    async fn create_organization_invitation<T: Into<String> + Send>(
        &self,
        organization_id: Uuid,
        email: T,
        role: OrganizationRole,
        invited_by: Uuid,
        token: String,
        expires_at: DateTime<Utc>,
    ) -> Result<OrganizationInvitation, sqlx::Error> {
        let invitation = sqlx::query_as!(
            OrganizationInvitation,
            r#"
            INSERT INTO organization_invitations (organization_id, email, role, invited_by, token, expires_at, status)
            VALUES ($1, $2, $3, $4, $5, $6, 'pending')
            RETURNING id, organization_id, email, role as "role: OrganizationRole", invited_by, token, expires_at, status, created_at, updated_at
            "#,
            organization_id,
            email.into(),
            role as OrganizationRole,
            invited_by,
            token,
            expires_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(invitation)
    }

    async fn get_invitation_by_token(
        &self,
        token: &str,
    ) -> Result<Option<(OrganizationInvitation, Organization, User)>, sqlx::Error> {
        let row = sqlx::query!(
            r#"
            SELECT
                i.id as "invitation_id!", i.organization_id, i.email, i.role as "invitation_role!: OrganizationRole",
                i.invited_by, i.token, i.expires_at, i.status, i.created_at as "invitation_created_at!", i.updated_at as "invitation_updated_at!",
                o.id as "org_id!", o.name as "org_name!", o.description, o.domain, o.settings,
                o.created_at as "org_created_at!", o.updated_at as "org_updated_at!",
                u.id as "user_id!", u.name as "user_name!", u.email as "user_email!", u.role as "user_role!: UserRole",
                u.email_otp, u.email_otp_expires_at, u.profile_data, u.default_organization_id,
                u.created_at as "user_created_at!", u.updated_at as "user_updated_at!"
            FROM organization_invitations i
            JOIN organizations o ON i.organization_id = o.id
            JOIN users u ON i.invited_by = u.id
            WHERE i.token = $1 AND i.expires_at > NOW()
            "#,
            token
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            let invitation = OrganizationInvitation {
                id: row.invitation_id,
                organization_id: row.organization_id,
                email: row.email,
                role: row.invitation_role,
                invited_by: row.invited_by,
                token: row.token,
                expires_at: row.expires_at,
                status: row.status,
                created_at: Some(row.invitation_created_at),
                updated_at: Some(row.invitation_updated_at),
            };

            let organization = Organization {
                id: row.org_id,
                name: row.org_name,
                description: row.description,
                domain: row.domain,
                settings: row.settings,
                created_at: Some(row.org_created_at),
                updated_at: Some(row.org_updated_at),
            };

            let user = User {
                id: row.user_id,
                name: row.user_name,
                email: row.user_email,
                role: row.user_role,
                email_otp: row.email_otp,
                email_otp_expires_at: row.email_otp_expires_at,
                profile_data: row.profile_data,
                default_organization_id: row.default_organization_id,
                created_at: Some(row.user_created_at),
                updated_at: Some(row.user_updated_at),
            };

            Ok(Some((invitation, organization, user)))
        } else {
            Ok(None)
        }
    }

    async fn get_invitation_by_email(
        &self,
        organization_id: Uuid,
        email: &str,
    ) -> Result<Option<OrganizationInvitation>, sqlx::Error> {
        let invitation = sqlx::query_as!(
            OrganizationInvitation,
            r#"
            SELECT id, organization_id, email, role as "role: OrganizationRole", invited_by, token, expires_at, status, created_at, updated_at
            FROM organization_invitations
            WHERE organization_id = $1 AND email = $2 AND status = 'pending' AND expires_at > NOW()
            "#,
            organization_id,
            email
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(invitation)
    }

    async fn update_invitation_status(
        &self,
        invitation_id: Uuid,
        status: &str,
    ) -> Result<OrganizationInvitation, sqlx::Error> {
        let invitation = sqlx::query_as!(
            OrganizationInvitation,
            r#"
            UPDATE organization_invitations
            SET status = $1, updated_at = NOW()
            WHERE id = $2
            RETURNING id, organization_id, email, role as "role: OrganizationRole", invited_by, token, expires_at, status, created_at, updated_at
            "#,
            status,
            invitation_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(invitation)
    }

    async fn get_user_invitations(
        &self,
        email: &str,
    ) -> Result<Vec<(OrganizationInvitation, Organization, User)>, sqlx::Error> {
        let rows = sqlx::query!(
            r#"
            SELECT
                i.id as "invitation_id!", i.organization_id, i.email, i.role as "invitation_role!: OrganizationRole",
                i.invited_by, i.token, i.expires_at, i.status, i.created_at as "invitation_created_at!", i.updated_at as "invitation_updated_at!",
                o.id as "org_id!", o.name as "org_name!", o.description, o.domain, o.settings,
                o.created_at as "org_created_at!", o.updated_at as "org_updated_at!",
                u.id as "user_id!", u.name as "user_name!", u.email as "user_email!", u.role as "user_role!: UserRole",
                u.email_otp, u.email_otp_expires_at, u.profile_data, u.default_organization_id,
                u.created_at as "user_created_at!", u.updated_at as "user_updated_at!"
            FROM organization_invitations i
            JOIN organizations o ON i.organization_id = o.id
            JOIN users u ON i.invited_by = u.id
            WHERE i.email = $1 AND i.status = 'pending' AND i.expires_at > NOW()
            ORDER BY i.created_at DESC
            "#,
            email
        )
        .fetch_all(&self.pool)
        .await?;

        let result = rows
            .into_iter()
            .map(|row| {
                let invitation = OrganizationInvitation {
                    id: row.invitation_id,
                    organization_id: row.organization_id,
                    email: row.email,
                    role: row.invitation_role,
                    invited_by: row.invited_by,
                    token: row.token,
                    expires_at: row.expires_at,
                    status: row.status,
                    created_at: Some(row.invitation_created_at),
                    updated_at: Some(row.invitation_updated_at),
                };

                let organization = Organization {
                    id: row.org_id,
                    name: row.org_name,
                    description: row.description,
                    domain: row.domain,
                    settings: row.settings,
                    created_at: Some(row.org_created_at),
                    updated_at: Some(row.org_updated_at),
                };

                let user = User {
                    id: row.user_id,
                    name: row.user_name,
                    email: row.user_email,
                    role: row.user_role,
                    email_otp: row.email_otp,
                    email_otp_expires_at: row.email_otp_expires_at,
                    profile_data: row.profile_data,
                    default_organization_id: row.default_organization_id,
                    created_at: Some(row.user_created_at),
                    updated_at: Some(row.user_updated_at),
                };

                (invitation, organization, user)
            })
            .collect();

        Ok(result)
    }

    async fn get_organization_invitations(
        &self,
        organization_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<(OrganizationInvitation, User)>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let rows = sqlx::query!(
            r#"
            SELECT
                i.id as "invitation_id!", i.organization_id, i.email, i.role as "invitation_role!: OrganizationRole",
                i.invited_by, i.token, i.expires_at, i.status, i.created_at as "invitation_created_at!", i.updated_at as "invitation_updated_at!",
                u.id as "user_id!", u.name as "user_name!", u.email as "user_email!", u.role as "user_role!: UserRole",
                u.email_otp, u.email_otp_expires_at, u.profile_data, u.default_organization_id,
                u.created_at as "user_created_at!", u.updated_at as "user_updated_at!"
            FROM organization_invitations i
            JOIN users u ON i.invited_by = u.id
            WHERE i.organization_id = $1
            ORDER BY i.created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            organization_id,
            limit as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await?;

        let result = rows
            .into_iter()
            .map(|row| {
                let invitation = OrganizationInvitation {
                    id: row.invitation_id,
                    organization_id: row.organization_id,
                    email: row.email,
                    role: row.invitation_role,
                    invited_by: row.invited_by,
                    token: row.token,
                    expires_at: row.expires_at,
                    status: row.status,
                    created_at: Some(row.invitation_created_at),
                    updated_at: Some(row.invitation_updated_at),
                };

                let user = User {
                    id: row.user_id,
                    name: row.user_name,
                    email: row.user_email,
                    role: row.user_role,
                    email_otp: row.email_otp,
                    email_otp_expires_at: row.email_otp_expires_at,
                    profile_data: row.profile_data,
                    default_organization_id: row.default_organization_id,
                    created_at: Some(row.user_created_at),
                    updated_at: Some(row.user_updated_at),
                };

                (invitation, user)
            })
            .collect();

        Ok(result)
    }

    async fn get_organization_invitation_count(
        &self,
        organization_id: Uuid,
    ) -> Result<i64, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM organization_invitations
            WHERE organization_id = $1
            "#,
            organization_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0))
    }
}

#[async_trait]
impl StorageExt for DBClient {
    async fn create_file<T: Into<String> + Send>(
        &self,
        filename: T,
        original_filename: T,
        file_path: T,
        file_size: i64,
        mime_type: T,
        file_type: FileType,
        access_type: FileAccessType,
        uploaded_by: Option<Uuid>,
        organization_id: Option<Uuid>,
        metadata: Option<serde_json::Value>,
    ) -> Result<File, sqlx::Error> {
        let file = sqlx::query_as!(
            File,
            r#"
            INSERT INTO files (filename, original_filename, file_path, file_size, mime_type, file_type, access_type, uploaded_by, organization_id, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id, filename, original_filename, file_path, file_size, mime_type,
                      file_type as "file_type: FileType", access_type as "access_type: FileAccessType",
                      uploaded_by, organization_id, metadata, download_count, is_deleted, created_at, updated_at
            "#,
            filename.into(),
            original_filename.into(),
            file_path.into(),
            file_size,
            mime_type.into(),
            file_type as FileType,
            access_type as FileAccessType,
            uploaded_by,
            organization_id,
            metadata
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(file)
    }

    async fn get_file(
        &self,
        file_id: Uuid,
    ) -> Result<Option<File>, sqlx::Error> {
        let file = sqlx::query_as!(
            File,
            r#"
            SELECT id, filename, original_filename, file_path, file_size, mime_type,
                   file_type as "file_type: FileType", access_type as "access_type: FileAccessType",
                   uploaded_by, organization_id, metadata, download_count, is_deleted, created_at, updated_at
            FROM files
            WHERE id = $1 AND is_deleted = FALSE
            "#,
            file_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(file)
    }

    async fn get_files(
        &self,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        access_type: Option<FileAccessType>,
        file_type: Option<FileType>,
        page: u32,
        limit: usize,
    ) -> Result<Vec<File>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let mut query = "SELECT id, filename, original_filename, file_path, file_size, mime_type, file_type, access_type, uploaded_by, organization_id, metadata, download_count, is_deleted, created_at, updated_at FROM files WHERE is_deleted = FALSE".to_string();
        let mut conditions = Vec::new();
        let mut param_count = 1;

        if user_id.is_some() {
            conditions.push(format!("uploaded_by = ${}", param_count));
            param_count += 1;
        }

        if organization_id.is_some() {
            conditions.push(format!("organization_id = ${}", param_count));
            param_count += 1;
        }

        if access_type.is_some() {
            conditions.push(format!("access_type = ${}", param_count));
            param_count += 1;
        }

        if file_type.is_some() {
            conditions.push(format!("file_type = ${}", param_count));
            param_count += 1;
        }

        if !conditions.is_empty() {
            query.push_str(" AND ");
            query.push_str(&conditions.join(" AND "));
        }

        query.push_str(&format!(" ORDER BY created_at DESC LIMIT ${} OFFSET ${}", param_count, param_count + 1));

        let mut query_builder = sqlx::query_as::<_, File>(&query);

        if let Some(uid) = user_id {
            query_builder = query_builder.bind(uid);
        }
        if let Some(oid) = organization_id {
            query_builder = query_builder.bind(oid);
        }
        if let Some(at) = access_type {
            query_builder = query_builder.bind(at);
        }
        if let Some(ft) = file_type {
            query_builder = query_builder.bind(ft);
        }

        let files = query_builder
            .bind(limit as i64)
            .bind(offset as i64)
            .fetch_all(&self.pool)
            .await?;

        Ok(files)
    }

    async fn get_file_count(
        &self,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        access_type: Option<FileAccessType>,
        file_type: Option<FileType>,
    ) -> Result<i64, sqlx::Error> {
        let mut query = "SELECT COUNT(*) FROM files WHERE is_deleted = FALSE".to_string();
        let mut conditions = Vec::new();
        let mut param_count = 1;

        if user_id.is_some() {
            conditions.push(format!("uploaded_by = ${}", param_count));
            param_count += 1;
        }

        if organization_id.is_some() {
            conditions.push(format!("organization_id = ${}", param_count));
            param_count += 1;
        }

        if access_type.is_some() {
            conditions.push(format!("access_type = ${}", param_count));
            param_count += 1;
        }

        if file_type.is_some() {
            conditions.push(format!("file_type = ${}", param_count));
            param_count += 1;
        }

        if !conditions.is_empty() {
            query.push_str(" AND ");
            query.push_str(&conditions.join(" AND "));
        }

        let mut query_builder = sqlx::query_scalar::<_, i64>(&query);

        if let Some(uid) = user_id {
            query_builder = query_builder.bind(uid);
        }
        if let Some(oid) = organization_id {
            query_builder = query_builder.bind(oid);
        }
        if let Some(at) = access_type {
            query_builder = query_builder.bind(at);
        }
        if let Some(ft) = file_type {
            query_builder = query_builder.bind(ft);
        }

        let count = query_builder
            .fetch_one(&self.pool)
            .await?;

        Ok(count)
    }

    async fn update_file_download_count(
        &self,
        file_id: Uuid,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE files
            SET download_count = download_count + 1, updated_at = NOW()
            WHERE id = $1
            "#,
            file_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn delete_file(
        &self,
        file_id: Uuid,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE files
            SET is_deleted = TRUE, updated_at = NOW()
            WHERE id = $1
            "#,
            file_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn create_file_permission(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        permission_type: PermissionType,
        granted_by: Option<Uuid>,
        expires_at: Option<DateTime<Utc>>,
    ) -> Result<FilePermission, sqlx::Error> {
        let permission = sqlx::query_as!(
            FilePermission,
            r#"
            INSERT INTO file_permissions (file_id, user_id, organization_id, permission_type, granted_by, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, file_id, user_id, organization_id, permission_type as "permission_type: PermissionType",
                      granted_by, expires_at, created_at, updated_at
            "#,
            file_id,
            user_id,
            organization_id,
            permission_type as PermissionType,
            granted_by,
            expires_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(permission)
    }

    async fn get_file_permissions(
        &self,
        file_id: Uuid,
    ) -> Result<Vec<FilePermission>, sqlx::Error> {
        let permissions = sqlx::query_as!(
            FilePermission,
            r#"
            SELECT id, file_id, user_id, organization_id, permission_type as "permission_type: PermissionType",
                   granted_by, expires_at, created_at, updated_at
            FROM file_permissions
            WHERE file_id = $1
            ORDER BY created_at DESC
            "#,
            file_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(permissions)
    }

    async fn check_file_permission(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        organization_id: Option<Uuid>,
        permission_type: PermissionType,
    ) -> Result<bool, sqlx::Error> {
        let count = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*)
            FROM file_permissions
            WHERE file_id = $1
              AND (user_id = $2 OR organization_id = $3)
              AND permission_type = $4
              AND (expires_at IS NULL OR expires_at > NOW())
            "#,
            file_id,
            user_id,
            organization_id,
            permission_type as PermissionType
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(count.unwrap_or(0) > 0)
    }

    async fn delete_file_permission(
        &self,
        permission_id: Uuid,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            DELETE FROM file_permissions
            WHERE id = $1
            "#,
            permission_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn log_file_access(
        &self,
        file_id: Uuid,
        user_id: Option<Uuid>,
        ip_address: Option<String>,
        user_agent: Option<String>,
        access_type: String,
    ) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            INSERT INTO file_access_logs (file_id, user_id, ip_address, user_agent, access_type)
            VALUES ($1, $2, $3, $4, $5)
            "#,
            file_id,
            user_id,
            ip_address,
            user_agent,
            access_type
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn get_file_access_logs(
        &self,
        file_id: Uuid,
        page: u32,
        limit: usize,
    ) -> Result<Vec<FileAccessLog>, sqlx::Error> {
        let offset = (page - 1) * limit as u32;

        let logs = sqlx::query_as!(
            FileAccessLog,
            r#"
            SELECT id, file_id, user_id, ip_address, user_agent, access_type, created_at
            FROM file_access_logs
            WHERE file_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            file_id,
            limit as i64,
            offset as i64
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(logs)
    }
}

