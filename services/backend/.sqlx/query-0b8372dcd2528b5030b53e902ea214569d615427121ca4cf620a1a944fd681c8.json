{"db_name": "PostgreSQL", "query": "\n            INSERT INTO files (filename, original_filename, file_path, file_size, mime_type, file_type, access_type, uploaded_by, organization_id, metadata)\n            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)\n            RETURNING id, filename, original_filename, file_path, file_size, mime_type,\n                      file_type as \"file_type: FileType\", access_type as \"access_type: FileAccessType\",\n                      uploaded_by, organization_id, metadata, download_count, is_deleted, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "filename", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "original_filename", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "file_path", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "file_size", "type_info": "Int8"}, {"ordinal": 5, "name": "mime_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "file_type: FileType", "type_info": {"Custom": {"name": "file_type", "kind": {"Enum": ["image", "pdf", "document", "csv", "code", "markdown", "html", "archive", "other"]}}}}, {"ordinal": 7, "name": "access_type: FileAccessType", "type_info": {"Custom": {"name": "file_access_type", "kind": {"Enum": ["public", "private", "organization"]}}}}, {"ordinal": 8, "name": "uploaded_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 9, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 10, "name": "metadata", "type_info": "Jsonb"}, {"ordinal": 11, "name": "download_count", "type_info": "Int4"}, {"ordinal": 12, "name": "is_deleted", "type_info": "Bool"}, {"ordinal": 13, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 14, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", {"Custom": {"name": "file_type", "kind": {"Enum": ["image", "pdf", "document", "csv", "code", "markdown", "html", "archive", "other"]}}}, {"Custom": {"name": "file_access_type", "kind": {"Enum": ["public", "private", "organization"]}}}, "<PERSON><PERSON>", "<PERSON><PERSON>", "Jsonb"]}, "nullable": [false, false, false, false, false, false, false, false, true, true, true, true, true, true, true]}, "hash": "0b8372dcd2528b5030b53e902ea214569d615427121ca4cf620a1a944fd681c8"}