{"db_name": "PostgreSQL", "query": "\n            INSERT INTO file_permissions (file_id, user_id, organization_id, permission_type, granted_by, expires_at)\n            VALUES ($1, $2, $3, $4, $5, $6)\n            RETURNING id, file_id, user_id, organization_id, permission_type as \"permission_type: PermissionType\",\n                      granted_by, expires_at, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "file_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 4, "name": "permission_type: PermissionType", "type_info": {"Custom": {"name": "permission_type", "kind": {"Enum": ["read", "write", "delete", "admin"]}}}}, {"ordinal": 5, "name": "granted_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", {"Custom": {"name": "permission_type", "kind": {"Enum": ["read", "write", "delete", "admin"]}}}, "<PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, true, true, false, true, true, true, true]}, "hash": "214c4937e3658b561c6cf79dd66c10319425f92a12d955c8773503e6509c3133"}