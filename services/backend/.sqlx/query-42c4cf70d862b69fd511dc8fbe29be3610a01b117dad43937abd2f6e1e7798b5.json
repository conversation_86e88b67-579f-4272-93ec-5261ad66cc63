{"db_name": "PostgreSQL", "query": "\n            SELECT id, organization_id, email, role as \"role: OrganizationRole\", invited_by, token, expires_at, status, created_at, updated_at\n            FROM organization_invitations\n            WHERE organization_id = $1 AND email = $2 AND status = 'pending' AND expires_at > NOW()\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "token", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Text"]}, "nullable": [false, false, false, false, false, false, false, false, true, true]}, "hash": "42c4cf70d862b69fd511dc8fbe29be3610a01b117dad43937abd2f6e1e7798b5"}