{"db_name": "PostgreSQL", "query": "\n            SELECT COUNT(*)\n            FROM file_permissions\n            WHERE file_id = $1\n              AND (user_id = $2 OR organization_id = $3)\n              AND permission_type = $4\n              AND (expires_at IS NULL OR expires_at > NOW())\n            ", "describe": {"columns": [{"ordinal": 0, "name": "count", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", {"Custom": {"name": "permission_type", "kind": {"Enum": ["read", "write", "delete", "admin"]}}}]}, "nullable": [null]}, "hash": "3739f22cbce909ff1a122d31800083b083eb5249af55f8117bcbb03e44f9602b"}