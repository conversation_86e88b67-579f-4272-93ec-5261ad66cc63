{"db_name": "PostgreSQL", "query": "\n            UPDATE organization_invitations\n            SET status = $1, updated_at = NOW()\n            WHERE id = $2\n            RETURNING id, organization_id, email, role as \"role: OrganizationRole\", invited_by, token, expires_at, status, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "token", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false, false, false, false, false, true, true]}, "hash": "84d75d346f92add6f3bc51a4c40c4618f01e07e10b4cbaec2fdf935866c73d77"}