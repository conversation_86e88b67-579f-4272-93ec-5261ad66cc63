{"db_name": "PostgreSQL", "query": "\n            SELECT id, file_id, user_id, ip_address, user_agent, access_type, created_at\n            FROM file_access_logs\n            WHERE file_id = $1\n            ORDER BY created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "file_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "ip_address", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_agent", "type_info": "Text"}, {"ordinal": 5, "name": "access_type", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "created_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "Int8"]}, "nullable": [false, false, true, true, true, false, true]}, "hash": "965d68194b8f1d97dbf1eb7bebc914ca836214364e7f98faafaf269b5d3d1339"}