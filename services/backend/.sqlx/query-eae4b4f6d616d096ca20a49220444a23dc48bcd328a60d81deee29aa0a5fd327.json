{"db_name": "PostgreSQL", "query": "\n            UPDATE organization_members\n            SET role = $1, updated_at = NOW()\n            WHERE id = $2\n            RETURNING id, organization_id, user_id, role as \"role: OrganizationRole\", invited_by, joined_at, settings, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "joined_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": [{"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}, "<PERSON><PERSON>"]}, "nullable": [false, false, false, false, true, true, true, true, true]}, "hash": "eae4b4f6d616d096ca20a49220444a23dc48bcd328a60d81deee29aa0a5fd327"}