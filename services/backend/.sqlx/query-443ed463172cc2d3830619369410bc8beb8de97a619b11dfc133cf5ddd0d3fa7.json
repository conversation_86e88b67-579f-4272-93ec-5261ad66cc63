{"db_name": "PostgreSQL", "query": "\n            SELECT\n                i.id as \"invitation_id!\", i.organization_id, i.email, i.role as \"invitation_role!: OrganizationRole\",\n                i.invited_by, i.token, i.expires_at, i.status, i.created_at as \"invitation_created_at!\", i.updated_at as \"invitation_updated_at!\",\n                u.id as \"user_id!\", u.name as \"user_name!\", u.email as \"user_email!\", u.role as \"user_role!: UserRole\",\n                u.email_otp, u.email_otp_expires_at, u.profile_data, u.default_organization_id,\n                u.created_at as \"user_created_at!\", u.updated_at as \"user_updated_at!\"\n            FROM organization_invitations i\n            JOIN users u ON i.invited_by = u.id\n            WHERE i.organization_id = $1\n            ORDER BY i.created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "invitation_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "invitation_role!: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "token", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "status", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 8, "name": "invitation_created_at!", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "invitation_updated_at!", "type_info": "Timestamptz"}, {"ordinal": 10, "name": "user_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 11, "name": "user_name!", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "user_email!", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 13, "name": "user_role!: UserRole", "type_info": {"Custom": {"name": "user_role", "kind": {"Enum": ["user", "admin"]}}}}, {"ordinal": 14, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 15, "name": "email_otp_expires_at", "type_info": "Timestamptz"}, {"ordinal": 16, "name": "profile_data", "type_info": "Jsonb"}, {"ordinal": 17, "name": "default_organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 18, "name": "user_created_at!", "type_info": "Timestamptz"}, {"ordinal": 19, "name": "user_updated_at!", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "Int8"]}, "nullable": [false, false, false, false, false, false, false, false, true, true, false, false, false, false, true, true, true, true, false, false]}, "hash": "443ed463172cc2d3830619369410bc8beb8de97a619b11dfc133cf5ddd0d3fa7"}