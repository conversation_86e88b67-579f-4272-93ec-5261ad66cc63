{"db_name": "PostgreSQL", "query": "\n            SELECT o.id, o.name, o.description, o.domain, o.settings, o.created_at, o.updated_at\n            FROM organizations o\n            JOIN organization_members om ON o.id = om.organization_id\n            WHERE om.user_id = $1\n            ORDER BY o.created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "description", "type_info": "Text"}, {"ordinal": 3, "name": "domain", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "Int8"]}, "nullable": [false, false, true, true, true, true, true]}, "hash": "411bf1bddffe40f429250747a0ee0743551ca6e2f43a5188934096930e3b43b1"}