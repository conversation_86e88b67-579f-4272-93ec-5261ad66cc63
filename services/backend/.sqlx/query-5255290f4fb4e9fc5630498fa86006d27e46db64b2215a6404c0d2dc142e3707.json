{"db_name": "PostgreSQL", "query": "\n            SELECT id, file_id, user_id, organization_id, permission_type as \"permission_type: PermissionType\",\n                   granted_by, expires_at, created_at, updated_at\n            FROM file_permissions\n            WHERE file_id = $1\n            ORDER BY created_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "file_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 4, "name": "permission_type: PermissionType", "type_info": {"Custom": {"name": "permission_type", "kind": {"Enum": ["read", "write", "delete", "admin"]}}}}, {"ordinal": 5, "name": "granted_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, true, true, false, true, true, true, true]}, "hash": "5255290f4fb4e9fc5630498fa86006d27e46db64b2215a6404c0d2dc142e3707"}