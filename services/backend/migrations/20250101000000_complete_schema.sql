-- Complete database schema for Saday Agent
-- Includes: Users, Organizations, Storage, and all related functionality

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('user', 'admin');
CREATE TYPE organization_role AS ENUM ('orgadmin', 'orgeditor', 'orgagent', 'orguser', 'orgviewer');
CREATE TYPE file_type AS ENUM ('image', 'pdf', 'document', 'csv', 'code', 'markdown', 'html', 'archive', 'other');
CREATE TYPE file_access_type AS ENUM ('public', 'private', 'organization');
CREATE TYPE permission_type AS ENUM ('read', 'write', 'delete', 'admin');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    email_otp VARCHAR(10),
    email_otp_expires_at TIMESTAMP WITH TIME ZONE,
    profile_data JSONB DEFAULT '{}'::jsonb,
    default_organization_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    settings JSONB DEFAULT '{}'::jsonb,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Organization members table
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL,
    user_id UUID NOT NULL,
    role organization_role NOT NULL DEFAULT 'orguser',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);

-- Organization invitations table
CREATE TABLE organization_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL,
    email VARCHAR(255) NOT NULL,
    role organization_role NOT NULL DEFAULT 'orguser',
    token VARCHAR(255) UNIQUE NOT NULL,
    invited_by UUID NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, email)
);

-- Files table
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type file_type NOT NULL,
    access_type file_access_type NOT NULL DEFAULT 'private',
    uploaded_by UUID,
    organization_id UUID,
    metadata JSONB DEFAULT '{}'::jsonb,
    download_count INTEGER DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- File permissions table
CREATE TABLE file_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL,
    user_id UUID,
    organization_id UUID,
    permission_type permission_type NOT NULL DEFAULT 'read',
    granted_by UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT file_permissions_user_or_org CHECK (
        (user_id IS NOT NULL AND organization_id IS NULL) OR
        (user_id IS NULL AND organization_id IS NOT NULL)
    )
);

-- File access logs table
CREATE TABLE file_access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    file_id UUID NOT NULL,
    user_id UUID,
    ip_address VARCHAR(45),
    user_agent TEXT,
    access_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- OTP requests table (for rate limiting)
CREATE TABLE otp_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    request_time TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraints
ALTER TABLE users
    ADD CONSTRAINT fk_users_default_organization
    FOREIGN KEY (default_organization_id) REFERENCES organizations(id) ON DELETE SET NULL;

ALTER TABLE organizations
    ADD CONSTRAINT fk_organizations_created_by
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE organization_members
    ADD CONSTRAINT fk_org_members_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_org_members_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE organization_invitations
    ADD CONSTRAINT fk_org_invitations_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_org_invitations_invited_by
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE files
    ADD CONSTRAINT fk_files_uploaded_by
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    ADD CONSTRAINT fk_files_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL;

ALTER TABLE file_permissions
    ADD CONSTRAINT fk_file_permissions_file
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_file_permissions_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_file_permissions_organization
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_file_permissions_granted_by
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE file_access_logs
    ADD CONSTRAINT fk_file_access_logs_file
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_file_access_logs_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- Create indexes for better performance
-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_default_organization_id ON users(default_organization_id);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Organizations indexes
CREATE INDEX idx_organizations_created_by ON organizations(created_by);
CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_created_at ON organizations(created_at);

-- Organization members indexes
CREATE INDEX idx_org_members_organization_id ON organization_members(organization_id);
CREATE INDEX idx_org_members_user_id ON organization_members(user_id);
CREATE INDEX idx_org_members_role ON organization_members(role);
CREATE INDEX idx_org_members_joined_at ON organization_members(joined_at);

-- Organization invitations indexes
CREATE INDEX idx_org_invitations_organization_id ON organization_invitations(organization_id);
CREATE INDEX idx_org_invitations_email ON organization_invitations(email);
CREATE INDEX idx_org_invitations_token ON organization_invitations(token);
CREATE INDEX idx_org_invitations_status ON organization_invitations(status);
CREATE INDEX idx_org_invitations_expires_at ON organization_invitations(expires_at);
CREATE INDEX idx_org_invitations_invited_by ON organization_invitations(invited_by);

-- Files indexes
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);
CREATE INDEX idx_files_organization_id ON files(organization_id);
CREATE INDEX idx_files_access_type ON files(access_type);
CREATE INDEX idx_files_file_type ON files(file_type);
CREATE INDEX idx_files_created_at ON files(created_at);
CREATE INDEX idx_files_is_deleted ON files(is_deleted);
CREATE INDEX idx_files_filename ON files(filename);

-- File permissions indexes
CREATE INDEX idx_file_permissions_file_id ON file_permissions(file_id);
CREATE INDEX idx_file_permissions_user_id ON file_permissions(user_id);
CREATE INDEX idx_file_permissions_organization_id ON file_permissions(organization_id);
CREATE INDEX idx_file_permissions_permission_type ON file_permissions(permission_type);
CREATE INDEX idx_file_permissions_expires_at ON file_permissions(expires_at);

-- File access logs indexes
CREATE INDEX idx_file_access_logs_file_id ON file_access_logs(file_id);
CREATE INDEX idx_file_access_logs_user_id ON file_access_logs(user_id);
CREATE INDEX idx_file_access_logs_created_at ON file_access_logs(created_at);
CREATE INDEX idx_file_access_logs_access_type ON file_access_logs(access_type);

-- OTP requests indexes
CREATE INDEX idx_otp_requests_email ON otp_requests(email);
CREATE INDEX idx_otp_requests_ip_address ON otp_requests(ip_address);
CREATE INDEX idx_otp_requests_request_time ON otp_requests(request_time);

-- Create GIN indexes for JSONB columns
CREATE INDEX idx_users_profile_data ON users USING GIN (profile_data);
CREATE INDEX idx_organizations_settings ON organizations USING GIN (settings);
CREATE INDEX idx_files_metadata ON files USING GIN (metadata);

-- Add table comments for documentation
COMMENT ON TABLE users IS 'User accounts with authentication and profile information';
COMMENT ON TABLE organizations IS 'Organizations that users can belong to';
COMMENT ON TABLE organization_members IS 'Membership relationships between users and organizations';
COMMENT ON TABLE organization_invitations IS 'Pending invitations to join organizations';
COMMENT ON TABLE files IS 'File storage metadata and access control information';
COMMENT ON TABLE file_permissions IS 'Granular file access permissions for users and organizations';
COMMENT ON TABLE file_access_logs IS 'Audit trail for file access events';
COMMENT ON TABLE otp_requests IS 'Rate limiting for OTP requests';

-- Add column comments
COMMENT ON COLUMN users.profile_data IS 'Flexible JSON storage for user profile information';
COMMENT ON COLUMN users.default_organization_id IS 'Users default organization for file uploads';
COMMENT ON COLUMN organizations.settings IS 'Organization-specific configuration and settings';
COMMENT ON COLUMN files.access_type IS 'Access level: public (no auth), private (user auth), organization (org members)';
COMMENT ON COLUMN files.metadata IS 'File-specific metadata like dimensions, page count, etc.';
COMMENT ON COLUMN file_permissions.expires_at IS 'Optional expiration date for temporary access';
COMMENT ON COLUMN organization_invitations.token IS 'Unique token for accepting invitations';
COMMENT ON COLUMN organization_invitations.expires_at IS 'Invitation expiration date';
