-- Drop storage-related tables and types

-- Drop indexes
DROP INDEX IF EXISTS idx_files_uploaded_by;
DROP INDEX IF EXISTS idx_files_organization_id;
DROP INDEX IF EXISTS idx_files_access_type;
DROP INDEX IF EXISTS idx_files_file_type;
DROP INDEX IF EXISTS idx_files_created_at;
DROP INDEX IF EXISTS idx_files_is_deleted;
DROP INDEX IF EXISTS idx_files_metadata;

DROP INDEX IF EXISTS idx_file_permissions_file_id;
DROP INDEX IF EXISTS idx_file_permissions_user_id;
DROP INDEX IF EXISTS idx_file_permissions_organization_id;

DROP INDEX IF EXISTS idx_file_access_logs_file_id;
DROP INDEX IF EXISTS idx_file_access_logs_user_id;
DROP INDEX IF EXISTS idx_file_access_logs_created_at;

-- Drop tables
DROP TABLE IF EXISTS file_access_logs;
DROP TABLE IF EXISTS file_permissions;
DROP TABLE IF EXISTS files;

-- Drop enum types
DROP TYPE IF EXISTS permission_type;
DROP TYPE IF EXISTS file_access_type;
DROP TYPE IF EXISTS file_type;
