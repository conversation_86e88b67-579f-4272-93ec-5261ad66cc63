-- Rollback complete database schema

-- Drop indexes
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_role;
DROP INDEX IF EXISTS idx_users_default_organization_id;
DROP INDEX IF EXISTS idx_users_created_at;
DROP INDEX IF EXISTS idx_users_profile_data;

DROP INDEX IF EXISTS idx_organizations_created_by;
DROP INDEX IF EXISTS idx_organizations_name;
DROP INDEX IF EXISTS idx_organizations_created_at;
DROP INDEX IF EXISTS idx_organizations_settings;

DROP INDEX IF EXISTS idx_org_members_organization_id;
DROP INDEX IF EXISTS idx_org_members_user_id;
DROP INDEX IF EXISTS idx_org_members_role;
DROP INDEX IF EXISTS idx_org_members_joined_at;

DROP INDEX IF EXISTS idx_org_invitations_organization_id;
DROP INDEX IF EXISTS idx_org_invitations_email;
DROP INDEX IF EXISTS idx_org_invitations_token;
DROP INDEX IF EXISTS idx_org_invitations_status;
DROP INDEX IF EXISTS idx_org_invitations_expires_at;
DROP INDEX IF EXISTS idx_org_invitations_invited_by;

DROP INDEX IF EXISTS idx_files_uploaded_by;
DROP INDEX IF EXISTS idx_files_organization_id;
DROP INDEX IF EXISTS idx_files_access_type;
DROP INDEX IF EXISTS idx_files_file_type;
DROP INDEX IF EXISTS idx_files_created_at;
DROP INDEX IF EXISTS idx_files_is_deleted;
DROP INDEX IF EXISTS idx_files_filename;
DROP INDEX IF EXISTS idx_files_metadata;

DROP INDEX IF EXISTS idx_file_permissions_file_id;
DROP INDEX IF EXISTS idx_file_permissions_user_id;
DROP INDEX IF EXISTS idx_file_permissions_organization_id;
DROP INDEX IF EXISTS idx_file_permissions_permission_type;
DROP INDEX IF EXISTS idx_file_permissions_expires_at;

DROP INDEX IF EXISTS idx_file_access_logs_file_id;
DROP INDEX IF EXISTS idx_file_access_logs_user_id;
DROP INDEX IF EXISTS idx_file_access_logs_created_at;
DROP INDEX IF EXISTS idx_file_access_logs_access_type;

DROP INDEX IF EXISTS idx_otp_requests_email;
DROP INDEX IF EXISTS idx_otp_requests_ip_address;
DROP INDEX IF EXISTS idx_otp_requests_request_time;

-- Drop tables (in reverse order of dependencies)
DROP TABLE IF EXISTS file_access_logs;
DROP TABLE IF EXISTS file_permissions;
DROP TABLE IF EXISTS files;
DROP TABLE IF EXISTS organization_invitations;
DROP TABLE IF EXISTS organization_members;
DROP TABLE IF EXISTS organizations;
DROP TABLE IF EXISTS otp_requests;
DROP TABLE IF EXISTS users;

-- Drop custom types
DROP TYPE IF EXISTS permission_type;
DROP TYPE IF EXISTS file_access_type;
DROP TYPE IF EXISTS file_type;
DROP TYPE IF EXISTS organization_role;
DROP TYPE IF EXISTS user_role;
